* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: #ffff;
    font-family: 'Poppins', sans-serif;
}

.header {
    display: flex;
    align-items: end;
    justify-content: space-between;
    padding: 0 7rem 1rem 7rem;
    width: 100%;
    position: absolute;
    top: 0;
    z-index: 1000;
}

.navbar-logo {
    width: 10%;
    height: 10%;
}


.nav-bar {
    display: flex;
    justify-content: center;
    gap: 25px;
    padding: 1.5rem 10rem 2.2rem 10rem;
    position: relative;
    background: url("images/menu-bg.svg") no-repeat top/cover;
    background-size: contain;
    width: 74%;
    flex: 1;
}


.navbar-toggle {
    display: none;
}



@media (max-width: 769px) {
    .header {
        align-items: center;
        justify-content: space-between;
        padding: 1rem 1rem 1rem 1rem;
        flex-wrap: wrap;
        position: absolute;
        z-index: 9;
        width: 100%;
    }

    .navbar-toggle {
        display: block;
        font-size: 28px;
        color: #fff;
        background: rgba(255, 255, 255, 0.15);
        border: none;
        padding: 4px 12px;
        border-radius: 8px;
        cursor: pointer;
    }


    .nav-item {
        position: relative;
        text-align: center;
    }

    .nav-bar {
        width: 100%;
        background: none;
        position: relative;
        padding: 0;
        flex: none;
        margin-top: 1rem;
    }

    .navbar-logo {
        width: 40%;
        height: 40%;
    }
}


.navbar-menu {
    list-style: none;
    display: flex;
    justify-content: space-around;
    width: 100%;
    gap: 20px;
    margin: 0;
    padding: 0 5rem;
    z-index: 1;
}

@media (max-width: 769px) {

    .navbar-menu {
        display: none;
        flex-direction: column;
        /* position: fixed; */
        top: 60px;
        left: 0;
        width: 100%;
        color: #fff;
        padding: 1rem;
        gap: 35px;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        border-radius: 0 0 16px 16px;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }

    .navbar-menu.open {
        display: flex;
    }

.nav-item {
    position: relative;
}

.nav-link {
    position: relative;
    color: #fff;
    text-decoration: none;
    font-size: 18px;;
    font-weight: 400;
    padding: 5px 50px;
    display: inline-block;
    transition: color 0.3s ease;
    font-family: 'Outfit', sans-serif;
    width: 100%;
}

.nav-link:hover {
    color: #fff;
    font-weight: 700;
}

.nav-link.active {
    color: #fff;
    font-weight: 700;
}

.nav-link::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -34px;
    width: 0;
    height: 2px;
    background: #fff;
    transition: width 0.4s ease;
    /* controls speed */
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
    /* animate from left to right */
}

.nav-link.active::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -34px;
    width: 100%;
    height: 1px;
    background: #ffffff40;
}

.hover-indicator {
    position: absolute;
    bottom: -5px;
    height: 2px;
    background: #00B9FF;;
    transition: all 0.3s ease;
    border-radius: 2px;
    display: none;
}


.btn-get-started {
    background-color: #00B9FF;
    color: white;
    padding: 8px 20px;
    border: none;
    border-radius: 5px;
    font-family: 'Outfit', sans-serif;
    font-size: 16px;
    cursor: pointer;
    font-weight: 400;
    width: 11rem;
    height: 50px;
    justify-content: center;
    align-items: center;
}

.btn-get-started:hover {
    box-shadow: 0px 2px 47.4px 0px #00B9FF;
}

@media (min-width: 1200px) and (max-width: 1536px) {
 .navbar-menu {
    padding: 0;
 }
  .nav-link.active::after {
    bottom: -0.8em; 
  }
  .nav-bar {
    padding: 1.5rem 5rem 2.2rem 5rem;;
  }
}
@media (min-width: 600px) and (max-width: 769px) {
.nav-link{
    font-size: 24px;
    padding: 20px 80px;
}
}

@media (max-width: 769px) {
    .btn-get-started {
        font-size: 16px;
        width: 10rem;
        height: 42px;
    }

    .nav-item {
        text-align: center;
    }
    .nav-link::after {
        bottom: -20px !important;
}
}

/* Hero Section */
.hero {
    position: relative;
    /* background: url("images/home-bgImg.svg") no-repeat top center/cover; */
    text-align: center;
    padding: 11rem 20px 11rem;
    font-family: 'Outfit', sans-serif;
    height: 100vh;
    overflow: hidden;
}
.hero::before {
    content: "";
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: url("images/home-bgImg.svg") no-repeat top center/cover;
    transition: transform 0.5s ease; 
    z-index: -1; 
}

.hero:hover::before {
   transform: scale(1.1);
   animation: zoomSlow 5s linear infinite alternate; 
}

.hero h1 {
    font-size: 146px;
    font-weight: 100;
    color: #fff;
    line-height: 1;
}

.hero h1 .highlight {
    color: #00B9FF;
    font-weight: 100;
    font-size: 100px;
    line-height: 1;
}
.hero-content{
    margin-top: 4rem;
}

.hero-subtext {
    color: #fff;
    max-width: 66rem;
    margin: 3rem auto 9rem;
    font-size: 18px;
    line-height: 2.4;
    font-family: 'Outfit', sans-serif;
    font-weight: 300;
}

/* Buttons */
.hero-buttons {
    display: flex;
    justify-content: center;
    gap: 24px;
}

.btn {
    padding: 12px 28px;
    border-radius: 5px;
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    font-family: 'Outfit', sans-serif;
    width: 12rem;
    height: 50px;
}

.btn-outline {
    background: #fff;
    color: #002138CC;
    border: 1px solid #fff;
}

.btn-outline:hover {
    background: #fff;
    color: #002138CC;
    box-shadow: 0px 2px 47.4px 0px #00B9FF;
}

.btn-primary {
    background: #00B9FF;
    color: #fff;
    border: 1px solid #00B9FF;
}

.btn-primary:hover {
    background: #00B9FF;
}

.btn:first-child:active {
    color: #002138CC;
    background-color: #ffff;
    border-color: #ffff;
}

/* Responsive */
@media (max-width: 768px){
    .hero h1 {
        font-size: 68px;
    }

    .hero h1 .highlight {
        color: #00B9FF;;
        font-weight: 200;
        font-size: 44px;
        line-height: 1;
    }

    .hero {
        padding: 10rem 20px 6rem;
        height: 50vh;
    }
    .hero-subtext {
    margin: 4rem auto 4rem;
    }
}
@media (max-width: 600px){
    .hero h1 {
        font-size: 68px;
    }

    .hero h1 .highlight {
        color: #00B9FF;;
        font-weight: 200;
        font-size: 44px;
        line-height: 1;
    }

    .hero {
        padding: 6rem 1rem 6rem;
        height: 100vh;
    }
    .hero-subtext {
    margin: 4rem auto 4rem;
    }
}
@media (max-width: 380px) {
     .hero h1 {
        font-size: 68px;
    }

    .hero h1 .highlight {
        color: #00B9FF;
        font-weight: 200;
        font-size: 44px;
        line-height: 1;
    }

    .hero {
        padding: 6rem 20px 6rem;
        height: 136vh;
    }
     .hero-subtext {
    margin: 4rem auto 4rem;
    }
}

.features-section {
    width: 100%;
    font-family: 'Outfit', sans-serif;
    background-color: #ffff;
    color: #002138CC;
}

.feature-row1 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 6rem;
    gap: 40px;
}

.vision-text-section{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #00B9FF08;
    padding: 4rem 0;
    font-family: 'Outfit', sans-serif;
}
.vision-text-section h4{
    color: #002138CC;
    font-size: 50px;
    font-weight: 300
}
.vision-text-section p{
    color: #002138CC;
    font-size: 18px;
    font-weight: 300;
    margin-top: 1rem;
    line-height: 2;
    max-width: 74rem;
    text-align: center;
}

.mission-section {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 40px;
  padding: 6rem 12rem;
  height: 80vh;
  font-family: 'Outfit', sans-serif;
}

.mission-left,
.mission-right {
  flex: 1;
  max-width: 45%;
  display: flex;
  flex-direction: column;
}

.mission-left {
  align-items: flex-start; 
  justify-content: flex-start;
  text-align: left;
}

.mission-right {
  align-items: flex-end;
  justify-content: flex-end;
  text-align: right;
}

.title-with-lineLeft {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  justify-content: center;
}
.title-with-lineRight {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  position: relative;
  justify-content: center;
}

.line-title {
  font-size: 50px;
  font-weight: 300;
  margin: 0;
  position: relative;
  color: #002138CC;
}

.line-title.left::before {
  content: "";
  position: absolute;
  top: 50%;
  left: -192px; 
  transform: translateY(-50%);
  width: 130px;
  height: 3px;
  background: linear-gradient(to right, #55D7FF);
}

.line-title.right::after {
  content: "";
  position: absolute;
  top: 50%;
  right: -192px;
  transform: translateY(-50%);
  width: 130px;
  height: 3px;
  background: linear-gradient(to left, #55D7FF);
}

.title-with-line .right{
    margin-left: auto;
}
.mission-left p,
.mission-right p {
  font-size: 18px;;
  line-height: 2;
  word-spacing: 2px;
  color: #002138CC;
  max-width: 31rem;
  margin-top: 2rem;
  font-weight: 300;
}

.leaf-iconLeft {
  width: 6rem;
  margin-bottom: 1.5rem;
}
.leaf-iconRight {
  width: 6rem;
  margin-bottom: 1.5rem;
}
/* Responsive */
@media (max-width: 768px) {
.mission-section {
    flex-direction: column;
    justify-content: left;
    width: 100%;
    height: auto;
    padding: 1rem 1rem;
}
.mission-left, .mission-right {
    max-width: 100%;
}
.vision-text-section {
    padding: 2rem 1rem 3rem;
}
.line-title.left::before {
    right:  -192px;
    left: auto;
}
.line-title.right::after {
    left:  -192px;
    right: auto;
    width: 160px;
}
.deliver-section .container {
    padding: 0rem;
}
}

.deliver-section {
  padding: 6rem 1rem;
  background: #00B9FF08;
  text-align: center;
  font-family: 'Outfit', sans-serif;
}

.deliver-title {
  font-size: 50px;
  font-weight: 300;
  margin-bottom: 2rem;
  color: #002138CC;
}

.deliver-subtitle {
  font-size: 18px;;
  font-weight: 300;
  color: #002138CC;
  max-width: 74rem;
  margin: 0 auto 3rem;
  line-height: 2;
}

.deliver-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  justify-content: center;
  margin-top: 6rem;
}

.deliver-card {
  position: relative;
  background: #fff;
  border-radius: 38px;
  padding: 3rem 2rem;
  transition: all 0.3s ease;
  text-align: center; 
  z-index: 1;
}

.deliver-card::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 38px;
    padding: 1px;
    background: linear-gradient(to right, #00B9FF, #0021380D);
    -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;    mask-composite: exclude;
    z-index: -1;
}

.deliver-card img {
  height: 58px;
  margin-bottom: 1rem;
}

.deliver-card h3 {
  font-size: 18px;;
  font-weight: 700;
  color: #002138CC;
  margin-bottom: 1rem;
}

.deliver-card p {
  font-size: 18px;;
  color: #002138CC;
  line-height: 1.8;
  font-weight: 300;
}

.deliver-card:hover {
  border-color: #00b9ff;
  box-shadow: 0 4px 20px rgba(0, 185, 255, 0.2);
  transform: translateY(-5px);
}

/* Responsive */
@media (max-width: 992px) {
  .deliver-grid {
    grid-template-columns: repeat(1, 1fr);
    gap: 1rem;
  }
  .deliver-card {
    padding: 2rem 1.5rem 1rem;
  }
}

/* @media (max-width: 576px) {
  .deliver-grid {s
    grid-template-columns: 1fr;
  }
} */


.feature-row2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 40px;
    background-color: #ffff;
    padding: 10rem 0;
}

.feature-image img {
    max-width: 420px;
    width: 100%;
    height: auto;
}

.feature-text {
    flex: 1;
}

.feature-textWrap {
    display: flex;
    flex-direction: column;
    margin-left: auto;
    justify-content: center;
    width: 70%;
    align-items: flex-start;
}

.feature-textWrap::before {
  content: "";
  position: absolute;
  left: 0;
  top: 10%; 
  transform: translateY(-50%);
  width: 12rem;
  height: 3px;
  background:  linear-gradient(to right, #55D7FF);
}

.feature-text h3 {
    font-size: 59px;
    color: #002138CC;
    margin-bottom: 10px;
    font-weight: 300;
}

.feature-text h2 {
    font-size: 50px;
    font-weight: 300;
    color: #002138CC;
    margin-bottom: 10px;
    line-height: 1.3;
}

.feature-text .subtitle {
    font-size: 20px;
    color: #002138CC;
    margin-bottom: 20px;
}

.feature-text .why {
    font-size: 20px;
    color: #002138CC;
    margin-bottom: 10px;
}

.feature-box {
    position: relative;
    border-radius: 38px;
    padding: 3rem 3rem;
    max-width: 40rem;
    background: transparent;
    word-spacing: 1px;
    line-height: 1.7;
    font-size: 18px;
    z-index: 1;
}

.feature-box::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 38px;
    padding: 1px;
    background: linear-gradient(to bottom, #00B9FF, #0021380D);

    -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask-composite: exclude;

    z-index: -1;
}


.feature-box p {
    font-size: 18px;;
    line-height: 1.7;
    color: #002138CC;
}

.feature-imageLeft {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 50%;
}

.feature-imageRight {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50%;
}


/* Responsive */
@media (max-width: 768px) {

    .feature-row1,
    .feature-row2 {
        flex-direction: column;
        text-align: center;
        padding: 1rem 0;
    }

    /* Row 1 */
    .feature-row1 .feature-imageLeft {
        order: 1;
        margin-bottom: 2rem;
        width: 100%;
        justify-content: center;
    }

    .feature-row1 .feature-text {
        order: 2;
        text-align: left;
        padding: 0 10%;
    }

    .feature-row1 img {
        width: 100%;
    }

    /* Row 2 */
    .feature-row2 .feature-imageRight {
        order: 1;
        margin-bottom: 2rem;
        width: 100%;
        justify-content: center;
    }

    .feature-row2 .feature-text {
        order: 2;
        padding: 0 1rem;
    }

    .feature-row2 img {
        width: 40%;
    }

    .feature-box {
        margin: 0 auto;
        padding: 1.5rem;
    }

    .feature-text h2 {
        font-size: 2rem;
    }

    .feature-text p.subtitle {
        font-size: 0.9rem;
    }

    .features-section {
        padding: 3rem 0% 1rem;
    }
    .feature-textWrap::before {
        top: -2%; 
        width: 7rem;
}
    .feature-textWrap {
        width: 100%;
    }
}
@media (max-width: 300px) {
    .feature-row2 img {
        width: 58%;
    }
}

.core-modules {
    padding: 6rem 14% 8rem;
    text-align: center;
    font-family: 'Outfit', sans-serif;
    background: #ffff;
}

.modules-header h2 {
    font-size: 50px;
    font-weight: 300;
    color: #002138CC;
    margin-bottom: 2rem;
}

.modules-header p {
    max-width: 74rem;
    margin: 0 auto 7rem auto;
    font-size: 18px;;
    color: #002138CC;
    line-height: 2;
    font-weight: 300;
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    justify-content: center;
    align-items: flex-start;
    margin: 0 auto 2rem;
}

.module-card {
    position: relative;
    background: #fff;
    border-radius: 38px;
    padding: 5rem 27px;
    transition: all 0.3s ease-in-out;
    cursor: pointer;
    width: 20rem;
    z-index: 1;
}

.module-card::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 38px;
    padding: 1px;
    background: linear-gradient(to right, #00B9FF, #0021380D);
    -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;    mask-composite: exclude;

    z-index: -1;
}


.module-card:hover {
    transform: translateY(-8px);
    box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.1);
}

.module-card img {
    max-width: 70px;
    margin-bottom: 2.5rem;
}

.module-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: #002138CC;
    margin-bottom: 1rem;
}
.module-card p {
    font-size: 18px;;
    font-weight: 300;
    color: #002138CC;
    line-height: 1.6;
}

/* Responsive */
@media (max-width: 768px) {
    .modules-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .core-modules {
        padding: 7rem 2rem;
    }

    .module-card {
        padding: 2rem 10px;
        width: auto;
        height: auto;
    }
}
@media (max-width: 600px){
    .modules-grid {
        grid-template-columns: repeat(1, 1fr);
    }

    .core-modules {
        padding: 7rem 1rem;
    }

    .module-card {
        padding: 2rem 10px 1rem;
        width: auto;
        height: auto;
    }
}


.milestones {
    position: relative;
    /* background: url("images/ai-bgImg.svg") no-repeat center center/cover; */
    padding: 0 10% 1rem 10%;
    color: #fff;
    text-align: center;
    font-family: 'Outfit', sans-serif;
    overflow: hidden; 
    z-index: 0;
}
.milestones::before {
  content: "";
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: url("images/ai-bgImg.svg") no-repeat center center/cover;
  transition: transform 0.5s ease; 
  z-index: -1;
}
.milestones:hover::before {
  transform: scale(1.1);
   animation: zoomSlow 5s linear infinite alternate; 
}

.milestones-overlay {
    padding: 50px 30px;
    border-radius: 20px;
}

.subtitle {
    font-size: 18px;
    margin-bottom: 15px;
    color: #ffff;
    font-weight: 300;
}

.milestones h2 {
    font-size: 50px;
    font-weight: 300;
    margin-bottom: 20px;
    color: #ffff;
    line-height: 1.3;
}

.description {
    max-width: 66rem;
    margin: 0 auto 50px auto;
    font-size: 18px;;
    color: #ffff;
    line-height: 1.6;
}

.milestones-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 30rem;
    padding: 0 16rem;
}

.stat-card {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-card .icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.stat-card h3 {
    font-size: 50px;
    font-weight: 300;
    color: #fff;
    margin-top: 1rem;
}

.stat-card p {
    font-size: 18px;;
    color: #ddd;
    margin-top: 5px;
}

.milestones-stats .stat-img {
    width: 42%;
    height: 42%;
}

/* Responsive */
@media (max-width: 768px) {
    .milestones-stats {
        grid-template-columns: repeat(2, 1fr);
        padding: 0;
        margin-top: 6rem;
        gap: 50px;
    }

    .milestones-stats .stat-img {
        width: 70%;
        height: 70%;
    }

    .milestones-overlay {
        padding: 4rem 0 3rem 0;
    }
}




.contact-section {
  background-color: #f7fdff;
  font-family: 'Outfit', sans-serif;
}

.contact-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
}

.contact-left {
  width: 50%;
  /* height: 100vh; */
  position: relative;
}

.contact-bg {
  width: 100%;
  height: 100%;
}

.contact-text {
  position: absolute;
  bottom: 14%;
  left: 16%;
  color: white;
  max-width: 64%;
}

.contact-text h2 {
  font-size: 68px;
  font-weight: 300;
  margin-bottom: 2.5rem;
  font-family: 'Outfit', sans-serif;
}

.contact-text p {
  font-size: 20px;
  line-height: 1.6;
  word-spacing: 2px;
  font-family: 'Outfit', sans-serif;
  max-width: 23rem;
  font-weight: 300;
}

.contact-right {
  flex: 1;
  padding: 2rem 1rem;
  border-radius: 12px;
  position: relative;
}

.contact-form-container {
  padding: 1rem 11rem 4rem;
}

.contact-right h3 {
  text-align: center;
  color: #002138 ;
  font-size: 40px;
  font-weight: 300;
  margin-bottom: 5rem;
}

.contact-form input,
.contact-form textarea {
  width: 100%;
  padding: 0.8rem;
  margin-bottom: 1.8rem;
  border: 1px solid #00213833;
  border-radius: 5px;
  font-size: 18px;;
  color: #0021384D;
  background-color: white;
  transition: all 0.3s ease;
  outline: none;
  resize: none;
}

.contact-form input:focus,
.contact-form textarea:focus,
.contact-form select:focus {
  border-color: #002138CC;
  color: #002138 ;
}

/* Placeholder default color */
.contact-form input::placeholder,
.contact-form textarea::placeholder,
.contact-form select::placeholder {
  color: #0021384D;
}

.contact-form input:focus::placeholder,
.contact-form textarea:focus::placeholder,
.contact-form select:focus::placeholder {
  color: #002138CC;
}

:focus-visible {
  outline: none;
}

.contact-form button {
  width: 100%;
  height: 62px;
  padding: 0 0.8rem;
  border: none;
  border-radius: 5px;
  background: #00B9FF;
  color: white;
  font-size: 18px;
  font-weight: 400;
  cursor: pointer;
  transition: background 0.3s ease;
  margin-top: 2rem;
}

.contact-form button:hover {
  /* background: linear-gradient(to bottom, #11894E, #0E6F3F, #042314); */
  background-color: #00B9FF;
}

/* Responsive */
@media (max-width: 768px) {
.contact-form-container {
    padding: 1rem 0rem;
}
.contact-text h2 {
    font-size: 50px;
}
.contact-text p {
    font-size: 19px;
}
.contact-text {
    left: 1rem;
}
}
    
    


.call-now {
    background: #f2fbff;
    font-family: 'Poppins', sans-serif;
    padding: 36px 10%;
}

.call-now-content {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4rem;
}

.call-now .left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.call-now .call-icon {
    width: 6rem;
    height: 8rem;
}

.call-now h2 {
    font-size: 60px;
    font-weight: 600;
    color:#002138CC;
    white-space: nowrap;
    margin: 0;
}

.call-now .right {
    text-align: left;
}

.call-now .right p {
    font-size: 20px;
    font-weight: 500;
    color: #002138CC;
    font-family: 'Poppins', sans-serif;
}

.call-now .phone {
    font-size: 20px;
    font-weight: 500;
    color: #00B9FF;
    text-decoration: none;
    transition: color 0.3s ease;
}

.call-now .phone:hover {
    color: #00B9FF;
}

/* Responsive */
@media (max-width: 768px) {
    .call-now-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .call-now .left {
        justify-content: center;
    }

    .call-now h2 {
        font-size: 40px;
    }
}


.footer {
    background: #002138;
    /* dark navy blue */
    color: #fff;
    padding: 50px 10%;
    font-family: 'Outfit', sans-serif;
}

.footer-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 30px;
}

.footer-logo {
    width: 150px;
}

.footer-center h3,
.footer-right h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.footer-center p,
.footer-right p {
    font-size: 0.9rem;
    margin: 5px 0;
    line-height: 1.6;
}

.footer-center a,
.footer-right a {
    color: #fff;
    text-decoration: none;
}

.footer-center a:hover,
.footer-right a:hover {
    color: #16a34a;
    /* green accent */
}

.social-icons {
    margin-top: 10px;
}

.social-icons a {
    display: inline-block;
    margin-right: 12px;
    font-size: 1.2rem;
    color: #fff;
    transition: color 0.3s ease;
}

.social-icons a:hover {
    color: #16a34a;
}

/* Responsive */
@media (max-width: 768px) {
    .footer-container {
        flex-direction: column;
        text-align: center;
    }

    .social-icons {
        justify-content: center;
    }
}

/* initial states */
.animate-left,
.animate-right,
.animate-top,
.animate-bottom {
    opacity: 0;
    transition: all 0.9s ease-out;
}

.animate-left {
    transform: translate3d(-100px, 0, 0);
}

.animate-right {
    transform: translate3d(100px, 0, 0);
}

.animate-top {
    transform: translate3d(0, -60px, 0);
}

.animate-bottom {
    transform: translate3d(0, 100px, 0);
}

/* when visible */
.animate-left.animate,
.animate-right.animate,
.animate-top.animate,
.animate-bottom.animate {
    opacity: 1;
    transform: translateX(0) translateY(0);
}


/* Pure Fade Animation */
@keyframes fadeOnly {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

/* Class for fade */
.fade-only {
    opacity: 0;
    animation: fadeOnly 2s ease-in forwards;
    animation-delay: 0.1s;
}

.scrollBtn {
    display: none;
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 99;
    border: none;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    background-color: transparent;
}

html,
body {
    overflow-x: hidden;
}
html {
  scroll-behavior: smooth;
}
a {
  text-decoration: none;
  color: inherit;
}
a:hover,
a:focus,
a:active {
  text-decoration: none;
  color: inherit;
}








.ai-hero {
    position: relative;
    /* background: url("images/AiHome-bgImg.svg") no-repeat top center/cover; */
    text-align: center;
    padding: 11rem 20px 11rem;
    font-family: 'Outfit', sans-serif;
    min-height: 100vh;
    overflow: hidden; 
    z-index: 0;
}

.ai-hero::before {
  content: "";
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: url("images/AiHome-bgImg.svg") no-repeat top center/cover;
  transition: transform 0.5s ease;
  z-index: -1;
}

.ai-hero:hover::before {
  transform: scale(1.1);
   animation: zoomSlow 5s linear infinite alternate; 
}

.ai-hero-content {
    margin-top: 9rem;
}

.ai-hero h1 {
    font-size: 146px;
    font-weight: 100;
    color: #fff;
    line-height: 1;
}

.ai-hero h1 .highlight {
    color: #55D7FF;
    font-weight: 100;
    font-size: 100px;
    line-height: 1;
}

.ai-hero-subtext {
    color: #fff;
    max-width: 76rem;
    margin: 4rem auto 7rem;
    font-size: 18px;;
    line-height: 2.4;
    font-family: 'Outfit', sans-serif;
    font-weight: 300;
}

/* Buttons */
.ai-hero-buttons {
    display: flex;
    justify-content: center;
    gap: 24px;
}

/* Responsive */
@media (max-width: 768px) {
    .ai-hero h1 {
        font-size: 82px;
    }

    .ai-hero h1 .highlight {
        color: #00B9FF;;
        font-weight: 200;
        font-size: 44px;
        line-height: 1;
    }

    .ai-hero {
        padding: 11rem 1rem 6rem;
        height: 50vh;
    }
    .ai-hero-content {
    margin-top: 0;
}
}

@media (min-width: 600px) and (max-width: 768px) {
    .ai-hero::before {
        height: 100%;
}
.ai-hero {
        padding: 11rem 20px 6rem;
        min-height: auto;
    }
}

.ai-features-section {
    padding: 8rem 10rem;
    font-family: 'Outfit', sans-serif;
    background-color: #FFF;
    /* background: url("images/whatwedoAi-bgImg.svg") no-repeat center/cover; */
    color: #F3F9F5;
    display: flex;
    justify-content: flex-end;
    min-height: 100vh;
    overflow: hidden; 
    z-index: 0;
    position: relative;
}

.ai-features-section::before {
  content: "";
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: url("images/whatwedoAi-bgImg.svg") no-repeat center/cover;
  transition: transform 0.5s ease;
  z-index: -1;
}

.ai-features-section:hover::before {
  transform: scale(1.1);
   animation: zoomSlow 5s linear infinite alternate; 
}

.ai-leaf-icon {
    width: 8rem;
    margin-bottom: 1rem;
}
.ai-contactLeaf-icon {
    margin-left: 6.5rem;
    width: 8rem;
    margin-bottom: -2.5rem;
}

.ai-feature-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
}

.ai-feature-text::before {
  content: "";
  position: absolute;
  left: -18%;
  top: 24%; 
  transform: translateY(-50%);
  width: 8rem;
  height: 3px;
  background:  linear-gradient(to right, #55D7FF);
}

.ai-feature-text h2 {
    font-size: 50px;
    font-weight: 300;
    margin-bottom: 1.5rem;
    line-height: 1.3;
    text-align: left;
}
.ai-feature-text p {
    font-weight: 300;
    font-size: 18px;;
    line-height: 2;
    word-spacing: 2px;
    max-width: 25rem;
    text-align: left;
}

.ai-feature-text .why {
    font-size: 20px;
    margin-bottom: 0.5rem;
    font-weight: 300;
}
.ai-feature-box-section{
    display: flex;
    justify-content: center;
    align-items: end;
}

.ai-feature-box {
    position: relative;
    border-radius: 38px;
    padding: 5rem 3rem;
    max-width: 40rem;
    background: transparent;
    word-spacing: 1px;
    line-height: 2;
    z-index: 1;
    height: max-content;
    font-size: 18px;
}

.ai-feature-box::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 38px;
    padding: 1px;
    background: linear-gradient(to bottom, #00B9FF, #002138);

    -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask-composite: exclude;

    z-index: -1;
}


.ai-feature-box p {
    font-size: 18px;;
    line-height: 1.7;
    font-weight: 300;
}

.ai-feature-imageLeft {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 50%;
}

.ai-feature-imageRight {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50%;
}


/* Responsive */
@media (max-width: 768px) {

    .ai-feature-row1,
    .ai-feature-row2 {
        flex-direction: column;
        text-align: center;
    }

    /* Row 1 */
    .ai-feature-row1 .ai-feature-imageLeft {
        order: 1;
        margin-bottom: 2rem;
        width: 100%;
        justify-content: center;
    }

    .ai-feature-row1 .ai-feature-text {
        order: 2;
        text-align: left;
        padding: 0 10%;
    }

    .ai-feature-row1 img {
        width: 100%;
    }

    /* Row 2 */
    .ai-feature-row2 .ai-feature-imageRight {
        order: 1;
        margin-bottom: 2rem;
        width: 100%;
        justify-content: center;
    }

    .ai-feature-row2 .ai-feature-text {
        order: 2;
        padding: 0 10%;
    }

    .ai-feature-row2 img {
        width: 100%;
    }

    .ai-feature-box {
        margin: 0 auto;
        padding: 1.5rem;
    }

    .ai-feature-text h2 {
        font-size: 1.4rem;
    }

    .ai-feature-text p.subtitle {
        font-size: 0.9rem;
    }

    .ai-features-section {
        padding: 2rem 5% 2rem;
        flex-direction: column;
    }
    .ai-feature-text::before {
        top: 16%;
        left: auto;
        right: -6%;
    }
    .ai-contactLeaf-icon {
        margin-left: 0rem;
        width: 6rem;
        margin-bottom: -2rem;
    }
    .contact-left {
        width: 100%;
    }
    .ai-leaf-icon {
    width: 5rem;
    margin-bottom: 1rem;
}
}

@media (min-width: 600px) and (max-width: 768px) {
.ai-features-section {
        min-height: 50vh;
}
}

.client-banner {
  background: #f2fbff;
  padding: 3rem 0;
  text-align: center;
  position: relative;
}

.client-title {
  display: inline-block;
  font-size: 50px;
  font-weight: 300;
  color: #002138CC;
}

.client-title::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 44%;
  height: 3px;
  background: linear-gradient(to right, #55D7FF);
}

.ai-core-modules {
    padding: 7rem 14%;
    text-align: center;
    font-family: 'Outfit', sans-serif;
    background: #f7fdff;
}

.ai-modules-header h2 {
    font-size: 50px;
    font-weight: 300;
    color: #002138CC;
    margin-bottom: 2rem;
}

.ai-modules-header p {
    max-width: 65rem;
    margin: 0 auto 6rem auto;
    font-size: 18px;;
    color: #002138CC;
    line-height: 1.6;
    font-weight: 300;
}

.ai-modules-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    justify-content: center;
    margin: 0 auto;
}

.ai-module-card {
    position: relative;
    background: transparent;
    border-radius: 38px;
    padding: 4rem 2rem 2rem;
    transition: all 0.3s ease-in-out;
    cursor: pointer;
    width: 20rem;
    z-index: 1;
}

.ai-module-card::after {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 38px;
    padding: 1px;
    background: linear-gradient(to right, #00B9FF, #0021381A);
    -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask-composite: exclude;
    z-index: -1;
}


.ai-module-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: #002138CC;
    font-family: 'Outfit', sans-serif;
    margin-bottom: 1rem;
}

.ai-module-card p {
    font-size: 18px;;
    font-weight: 300;
    color: #002138CC;
    line-height: 1.6;
}

.ai-module-card:hover {
    transform: translateY(-8px);
    box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.1);
}

.ai-module-card img {
    max-width: 70px;
    margin-bottom: 2.5rem;
}


/* Responsive */
@media (max-width: 768px) {
    .ai-modules-grid {
        grid-template-columns: repeat(1, 1fr);
        place-items: center;
    }

    .ai-core-modules {
        padding: 4rem 1rem 5rem;
    }

    .ai-module-card {
        padding: 2rem 10px;
        width: auto;
        height: auto;
    }
}

.clients-section {
  padding: 4rem 0;
  background: #fff;
}

.clients-logos {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8rem; 
  padding: 8rem 0;
}

.clients-logos img {
  transition: transform 0.3s ease, filter 0.3s ease;
}
.client1-img{
  max-height: 4rem; 
}
.client2-img{
  max-height: 5rem; 
}
.client3-img{
  max-height: 8rem; 
}

.clients-logos img:hover {
  transform: scale(1.1);
}

.clients-text-section{
    margin-top: 7rem;
    margin-bottom: 4rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.clients-text-section h4{
    color: #002138CC;
    font-size: 50px;
    font-weight: 300;
    margin-bottom: 2rem;
}
.clients-text-section p{
    color: #002138CC;
    font-size: 18px;;
    font-weight: 300;
    margin-top: 1rem;
    line-height: 1.8;
    max-width: 60rem;
    text-align: center;
}
/* Responsive */
@media (max-width: 768px) {
 .clients-logos {
   flex-direction: column;
   padding: 1rem 0;
   gap: 3rem;
}
.clients-text-section {
    padding: 0 1rem;
}
.clients-text-section h4 {
    text-align: center;
}
.client-title::after {
    width: 24%;
}
}



/* Keyframes for continuous zoom */
@keyframes zoomSlow {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.2);
  }
}