<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>onCreativ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-7N8eA70/p1I+ro0p4ASclJ8o9/8PVyyXUm9gA84aU/j/dJj7YfjpbuBvv0dJ+Z3E" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Slab&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@100;200;300;400;500;600;700&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" type="text/css" href="style.css" />
</head>

<body>
    <div class="home-section">
        <!-- Navbar -->
        <div class="header">
            <img class="navbar-logo animate-left" src="images/OnCreativ_headerLogo.svg" alt="inventa logo">

            <button class="navbar-toggle" onclick="toggleMenu()">☰</button>
            <nav class="nav-bar animate-top">
                <div class="hover-indicator" id="hoverIndicator"></div>
                <ul class="navbar-menu" id="navbarMenu">
                    <li class="nav-item">
                        <a class="nav-link active" href="#">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#industry">Industry</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="aiSolution.html">Our Ai Solutions</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contactUs">Contact Us</a>
                    </li>
                </ul>
            </nav>
            <button class="btn-get-started animate-right mt-4 mt-md-0 d-none d-md-flex"><a href="#contactUs">Get started</a></button>
        </div>

        <!-- Scroll to Top button -->
        <button onclick="scrollToTop()" id="scrollBtn" class="scrollBtn" title="Scroll Top"><img
                src="images/scrollToTop.svg" width="45px" height="45px"></button>


        <section class="hero">
            <div class="hero-content">
                <h1 class="fade-only">
                    Smarter Operations <br>
                    <span class="highlight">Stronger Results</span>
                </h1>
                <p class="hero-subtext animate-top">
                    At onCreativ, we believe the future of work is intelligent, connected, and effortless. Too many organizations run on fragmented systems that slow people down and create silos. We built onCreativ to change that.

                </p>
                <div class="hero-buttons mt-5 animate-bottom">
                    <button class="btn btn-outline"><a href="#contactUs">Get started</a></button>
                </div>
            </div>
        </section>

        <section class="features-section" id="about">
            <div class="vision-text-section animate-bottom">
            <h4 class="mt-1">Our vision</h4>
            <p>Our vision is simple: empower teams with AI-driven process intelligence automation (PIA) so they can focus on outcomes, not busywork. By combining automation with adaptive AI agents, we help organizations boost productivity, strengthen collaboration, and unlock growth.</p>
        </div>

        <section class="mission-section">
            <div class="mission-left animate-left">
                <div class="title-with-lineLeft">
                    <img src="images/leaf.svg" alt="leaf icon" class="leaf-iconLeft">
                    <h2 class="line-title left">Our Mission</h2>
                </div>
                <p>
                    At onCreativ, we believe work should be intelligent, connected, and effortless. Our mission is simple: empower organizations with AI-driven process intelligence automation (PIA) so teams can focus on outcomes, not busywork.
                </p>
            </div>
        
            <div class="mission-right animate-right">
                <div class="title-with-lineRight">
                     <img src="images/leaf.svg" alt="leaf icon" class="leaf-iconRight">
                    <h2 class="line-title right">Who We Are ?</h2>                   
                </div>
                <p>
                    Founded on over a decade of expertise in workflow automation and AI solutions, onCreativ bridges the gap between people, processes, and technology. We’re not just building software we’re helping organizations reimagine the future of work.
                </p>
            </div>
        </section>


        <section class="deliver-section">
            <div class="container">
                <h2 class="deliver-title animate-top">What We Deliver</h2>
                <p class="deliver-subtitle animate-top">
                  At onCreativ, we bring together innovation and intelligence to help businesses transform with confidence. Our solutions are designed to simplify complexity, accelerate performance, and ensure lasting value.
                </p>

                <div class="deliver-grid">
                    <div class="deliver-card animate-left">
                        <img src="images/deliver1.svg" alt="Clarity Icon">
                        <h3>Clarity</h3>
                        <p>Unify fragmented systems into one platform.</p>
                    </div>

                    <div class="deliver-card animate-left">
                        <img src="images/deliver2.svg" alt="Intelligence Icon">
                        <h3>Intelligence</h3>
                        <p>AI agents that adapt to your workflows.</p>
                    </div>

                    <div class="deliver-card animate-right">
                        <img src="images/deliver3.svg" alt="Scalability Icon">
                        <h3>Scalability</h3>
                        <p>Solutions that grow with your needs.</p>
                    </div>

                    <div class="deliver-card animate-right">
                        <img src="images/deliver4.svg" alt="Security Icon">
                        <h3>Security</h3>
                        <p>Enterprise-grade compliance and governance.</p>
                    </div>
                </div>
            </div>
        </section>



            <div class="feature-row2">
                <div class="feature-text text-start animate-left">
                    <span class="feature-textWrap">
                    <p class="why">Why choose us?</p>
                    <h2 class="textAlign-right">Smart Solutions, Aligned<br> with Your Needs</h2>
                    </span>
                    <div class="feature-box ms-auto">
                    <p><ul> 
                    <li><strong>Efficiency at Scale</strong> – Automate manual work and accelerate business processes.</li>
                    <li><strong>Connected Platform</strong> – Replace disconnected systems with one unified solution.</li>
                    <li><strong>Future-Ready</strong> – AI-driven insights ensure your organization stays ahead.</li>
                    <li><strong>AI Agents Built In</strong> – Intelligent assistants that learn, adapt, and support every department.</li>
                    <li><strong>Security First</strong> – Built with enterprise-grade compliance and governance.</li>
                    <li><strong>Flexible Growth</strong> – Start with one solution, expand as your needs evolve.</li>
                    </ul>
                    </p>
                    </div>
                </div>
                <div class="feature-imageRight animate-right">
                    <img src="images/feature-rightImg.svg" alt="Tailored Solutions">
                </div>
            </div>
        </section>


        <section class="core-modules" id="industry">
            <div class="modules-header animate-top">
                <h2>Industry Solutions</h2>
                <p>Every industry thrives when intelligence drives innovation. onCreativ delivers AI-powered industry solutions designed to accelerate results and adapt seamlessly to sector-specific goals.
                </p>
            </div>

            <div class="modules-grid">
                <div class="module-card animate-left">
                    <img src="images/education.svg" alt="Form Builder">
                    <h3>Education</h3>
                    <p>Smarter admissions, advising, and student success powered by AI-driven planning and workflows.</p>
                </div>
                <div class="module-card animate-left">
                    <img src="images/healthcare.svg" alt="Workflow Builder">
                    <h3>Healthcare</h3>
                    <p>Patient intake, scheduling, and compliance streamlined with secure automation and intelligent agents.</p>
                </div>
                <div class="module-card animate-right">
                    <img src="images/government.svg" alt="Document Management">
                    <h3>Government</h3>
                    <p>Citizen services delivered faster with digital portals, approvals automation, and centralized process intelligence.</p>
                </div>
                <div class="module-card animate-right">
                    <img src="images/enterprise.svg" alt="E-Signature">
                    <h3>Enterprise </h3>
                    <p>Scalable AI-driven workflows across finance, HR, operations, and IT for clarity, agility, and growth.</p>
                </div>
            </div>
        </section>


        <!-- <section class="milestones">
            <div class="milestones-overlay">
                <p class="subtitle animate-top">Milestones that we proudly accomplished.</p>
                <h2 class="animate-top">
                    Efficiency Redefined: Accelerating <br>
                    Your Business Potential
                </h2>
                <p class="description animate-top">
                    Revvdd accelerates your growth trajectory by streamlining processes and empowering your team to
                    achieve more in less time. With our intuitive platform and customizable features, you’ll experience
                    a new level of efficiency that drives success.
                </p>

                <div class="milestones-stats">
                    <div class="stat-card">
                        <img src="images/stat-img1.svg" class="stat-img">
                        <h3 class="counting" data-count="65" data-suffix="+">0</h3>
                        <p>Happy Clients</p>
                    </div>
                    <div class="stat-card">
                        <img src="images/stat-img2.svg" class="stat-img">
                        <h3 class="counting" data-count="40" data-suffix="+">0</h3>
                        <p>Expert Employees</p>
                    </div>
                    <div class="stat-card">
                        <img src="images/stat-img3.svg" class="stat-img">
                        <h3 class="counting" data-count="9.1" data-suffix="">0</h3>
                        <p>NPS Score</p>
                    </div>
                    <div class="stat-card">
                        <img src="images/stat-img4.svg" class="stat-img">
                        <h3 class="counting" data-count="42" data-suffix="%">0</h3>
                        <p>YOY Growth</p>
                    </div>
                </div>
            </div>
        </section> -->


         
        <section class="contact-section" id="contactUs">
            <div class="contact-container">
                <div class="contact-left">
                    <img src="images/contactUs-bgImg.svg" alt="Contact Background" class="contact-bg" />
                    <div class="contact-text animate-left">
                        <h2 class="d-flex">Let’s Build Smarter <br>Together</h2>
                        <p class="d-flex">We’d love to hear how we can help transform your operations with process intelligence automation.</p>                      
                    </div>
                </div>
          
                <div class="contact-right">
                    <div class="contact-form-container">
                        <img src="images/contactUs-leaf.svg" alt="leaf icon" class="ai-contactLeaf-icon animate-top" />
                        <h3 class="animate-top">Contact Us</h3>
                        <form class="contact-form animate-right">
                            <input type="text" placeholder="Enter your Name" required>
                            <input type="email" placeholder="Enter your Email" required>
                            <input type="text" placeholder="Enter your Phone Number (optional)">
                            <textarea rows="4" placeholder="Enter your Message..." required></textarea>
                            <button type="submit">Submit</button>
                        </form>
                        
                    </div>
                </div>
            </div>
        </section>


        <section class="call-now">
            <div class="call-now-content">
                <div class="left animate-left">
                    <img src="images/callNow.svg" alt="Call Icon" class="call-icon" />
                    <h2>Call Now!</h2>
                </div>
                <div class="right animate-left">
                    <p class="mb-1">Revolutionize Your</p>
                    <a href="tel:+12062748655" class="phone">+1**************</a>
                </div>
            </div>
        </section>

        <footer class="footer">
            <div class="footer-container">
                <!-- Left -->
                <div class="footer-left">
                    <img src="images/OnCreativ_headerLogo.svg" alt="onCreativ Logo" class="footer-logo" />
                </div>

                <!-- Center -->
                <div class="footer-center">
                    <h3>Contact Us</h3>
                    <p><strong>Office Locations:</strong></p>
                    <p>5608 17th Avenue NW, Suite 717, Seattle, WA 98107</p>
                    <p>6543 S Las Vegas Blvd, Las Vegas, NV 89109</p>
                    <p><a href="tel:+12062748655">+1**************</a></p>
                    <!-- <p><a href="mailto:<EMAIL>"><EMAIL></a></p> -->
                </div>

                <!-- Right -->
                <div class="footer-right">
                    <h3>Contact time</h3>
                    <p>09:00 AM - 6:00 PM</p>
                    <p>Monday - Friday</p>
                    <p><strong>Follow Us</strong></p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i>




                    </div>





                    <script>
                        const animatedElements = document.querySelectorAll(
                            '.animate-left, .animate-right, .animate-top, .animate-bottom'
                        );

                        const observer = new IntersectionObserver((entries) => {
                            entries.forEach(entry => {
                                if (entry.isIntersecting) {
                                    entry.target.classList.add("animate");

                                    if (entry.target.id === 'bannerSection') {
                                        entry.target.classList.add("active");
                                    }
                                } else {
                                    entry.target.classList.remove("animate");
                                }
                            });
                        }, { threshold: 0.2 });

                        animatedElements.forEach(el => observer.observe(el));
                    </script>


                    <script>
                        (() => {
                            const navLinks = document.querySelectorAll('.nav-link');
                            const indicator = document.querySelector('.hover-indicator');

                            function moveIndicator(link) {
                                const rect = link.getBoundingClientRect();
                                const parentRect = link.parentElement.parentElement.getBoundingClientRect(); // ul
                                indicator.style.width = `${rect.width}px`;
                                indicator.style.left = `${rect.left - parentRect.left}px`;
                                indicator.style.opacity = 1;
                            }

                            navLinks.forEach(link => {
                                link.addEventListener('mouseenter', () => moveIndicator(link));
                            });

                            document.querySelector('.navbar-menu').addEventListener('mouseleave', () => {
                                indicator.style.opacity = 0;
                            });

                            // Initial move to active item
                            const activeLink = document.querySelector('.nav-link.active');
                            if (activeLink) {
                                moveIndicator(activeLink);
                            }
                        })();
                    </script>

                    <script>
                        const countingElements = document.querySelectorAll('.counting');

                        const countObserver = new IntersectionObserver((entries) => {
                            entries.forEach(entry => {
                                if (entry.isIntersecting) {
                                    const element = entry.target;
                                    const endValue = parseFloat(element.getAttribute('data-count'));
                                    const suffix = element.getAttribute('data-suffix') || "";
                                    const duration = 2000;
                                    const frameRate = 60;
                                    const totalFrames = Math.round(duration / (1000 / frameRate));
                                    let frame = 0;

                                    function animateCount() {
                                        frame++;
                                        let progress = frame / totalFrames;
                                        if (progress > 1) progress = 1;

                                        let currentValue = endValue * progress;

                                        element.textContent = endValue % 1 !== 0
                                            ? currentValue.toFixed(1) + suffix
                                            : Math.floor(currentValue) + suffix;

                                        if (progress < 1) {
                                            requestAnimationFrame(animateCount);
                                        }
                                    }

                                    // reset to 0 before starting
                                    element.textContent = "0" + suffix;
                                    animateCount();
                                }
                            });
                        }, { threshold: 0.3 });

                        countingElements.forEach(el => countObserver.observe(el));
                    </script>



                    <script>
                        function toggleMenu() {
                            const menu = document.getElementById('navbarMenu');
                            menu.classList.toggle('open');
                        }

                        document.addEventListener('DOMContentLoaded', () => {
                            const menu = document.getElementById('navbarMenu');
                            const toggleButton = document.querySelector('.navbar-toggle');

                            // Close menu when any nav link is clicked
                            document.querySelectorAll('#navbarMenu .nav-link').forEach(link => {
                                link.addEventListener('click', () => {
                                    menu.classList.remove('open');
                                });
                            });

                            // Close when clicking outside
                            document.addEventListener('click', (event) => {
                                if (
                                    menu.classList.contains('open') &&
                                    !menu.contains(event.target) &&
                                    !toggleButton.contains(event.target)
                                ) {
                                    menu.classList.remove('open');
                                }
                            });
                        });
                    </script>

                    <script>
                        // Show the button when scrolled down 20px from the top
                        window.onscroll = function () {
                            scrollFunction();
                        };

                        function scrollFunction() {
                            const scrollBtn = document.getElementById("scrollBtn");
                            if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                                scrollBtn.style.display = "block";
                            } else {
                                scrollBtn.style.display = "none";
                            }
                        }

                        // Scroll to the top of the document
                        function scrollToTop() {
                            document.body.scrollTop = 0; // For Safari
                            document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
                        }
                    </script>








</body>

</html>