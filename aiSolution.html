<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>onCreativ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-7N8eA70/p1I+ro0p4ASclJ8o9/8PVyyXUm9gA84aU/j/dJj7YfjpbuBvv0dJ+Z3E" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Slab&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@100;200;300;400;500;600;700&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" type="text/css" href="style.css" />
</head>

<body>
    <div class="home-section">
        <!-- Navbar -->
        <div class="header">
            <img class="navbar-logo animate-left" src="images/OnCreativ_headerLogo.svg" alt="inventa logo">

            <button class="navbar-toggle" onclick="toggleMenu()">☰</button>
            <nav class="nav-bar animate-top">
                <div class="hover-indicator" id="hoverIndicator"></div>
                <ul class="navbar-menu" id="navbarMenu">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.html#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.htmlindustry.html">Industry</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="aiSolution.html">Our Ai Solutions</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contactUs">Contact Us</a>
                    </li>
                </ul>
            </nav>
            <button class="btn-get-started animate-right mt-4 mt-md-0 d-none d-md-flex"><a href="#contactUs">Get started</a></button>
        </div>

        <!-- Scroll to Top button -->
        <button onclick="scrollToTop()" id="scrollBtn" class="scrollBtn" title="Scroll Top"><img
                src="images/scrollToTop.svg" width="45px" height="45px"></button>


        <section class="ai-hero">
            <div class="ai-hero-content">
                <h1 class="fade-only">
                    Smarter Decisions <br>
                    <span class="highlight">Faster Growth</span>
                </h1>
                <p class="ai-hero-subtext animate-top">
                    At onCreativ, we believe every business runs better when intelligence is at its core. Our AI-powered business solutions help organizations automate processes, unlock data-driven insights, and accelerate growth all while adapting seamlessly to your unique goals
                </p>
            </div>
        </section>

        
        <section class="ai-core-modules">
            <div class="ai-modules-header animate-top">
                <h2>AI Business Solutions</h2>
                <p>Every department runs better with intelligence at its core. onCreativ delivers AI-powered assistants and process solutions designed to accelerate results and adapt to your goals
                </p>
            </div>

            <div class="ai-modules-grid">
                <div class="ai-module-card">
                    <img src="images/people.svg" alt="Form Builder">
                    <h3>People Operations </h3>
                    <p>Automate recruiting, onboarding, and performance workflows while AI agents provide personalized employee support.</p>
                </div>
                <div class="ai-module-card">
                    <img src="images/finance.svg" alt="Workflow Builder">
                    <h3>Finance & Operations </h3>
                    <p>Streamline approvals, compliance, and reporting with intelligent routing and anomaly detection.</p>
                </div>
                <div class="ai-module-card">
                    <img src="images/service.svg" alt="Document Management">
                    <h3>IT & Service Management </h3>
                    <p>Resolve tickets, automate requests, and optimize resources with AI-driven workflows.</p>
                </div>
                <div class="ai-module-card">
                    <img src="images/hub.svg" alt="E-Signature">
                    <h3>Engagement Hub </h3>
                    <p>Manage relationships with customers, partners, and vendors through predictive insights, proactive recommendations, and intelligent engagement.</p>
                </div>
            </div>
        </section>

        <section class="ai-features-section"> 
                <div class="ai-feature-text text-end animate-left">
                    <img src="images/leaf.svg" alt="leaf icon" class="ai-leaf-icon" />
                    <p class="why">Why We Do?</p>
                    <h2 class="ai-textAlign-right">Process Intelligence<br> Automation at the Core</h2>
                    <p>onCreativ is more than a platform it’s a partner in transformation. Our PIA engine continuously learns from your workflows, making operations smarter with every interaction.</p>
                    </div>
                    <div class="ai-feature-box-section">
                    <div class="ai-feature-box ms-auto animate-right">
                    <ul>                    
                    <li>Automation that thinks ahead – eliminate repetitive work while AI suggests improvements.</li>
                    <li>Collaboration that scales – connect people, data, and processes into one intelligent ecosystem.</li>
                    <li>Decisions that drive impact – real-time analytics and AI insights that help teams act faster and smarter.</li>
                    </ul>
                    </div>
                    </div>
        </section>

        <section class="client-banner animate-right">
            <h2 class="client-title">Clients</h2>
        </section>


        <section class="clients-section">
            <div class="clients-logos animate-bottom">
            <img src="images/client1.svg" class="client1-img" alt="Sharp Logo">
            <img src="images/client2.svg" class="client2-img" alt="Raich Logo">
            <img src="images/client3.svg" class="client3-img" alt="Travelers Logo">
        </div>
        <div class="clients-text-section animate-top">
            <h4>Ready to Transform?</h4>
            <p>onCreativ helps organizations of every size reimagine productivity and collaboration through process intelligence automation. Whether you want to reduce overhead, improve collaboration, or become a truly data-driven business, onCreativ delivers the clarity and tools to help you thrive.</p>
        </div>
    </section>


       
         
        <section class="contact-section" id="contactUs">
            <div class="contact-container">
                <div class="contact-left">
                    <img src="images/contactUs-bgImg.svg" alt="Contact Background" class="contact-bg" />
                    <div class="contact-text animate-left">
                        <h2 class="d-flex fa">Let’s Build Smarter <br>Together</h2>
                        <p class="d-flex">We’d love to hear how we can help transform your operations with process intelligence automation.</p>                      
                    </div>
                </div>
          
                <div class="contact-right">
                    <div class="contact-form-container">
                        <img src="images/contactUs-leaf.svg" alt="leaf icon" class="ai-contactLeaf-icon animate-top" />
                        <h3 class="animate-top">Contact Us</h3>
                        <form class="contact-form animate-right">
                            <input type="text" placeholder="Enter your Name" required>
                            <input type="email" placeholder="Enter your Email" required>
                            <input type="text" placeholder="Enter your Phone Number (optional)">
                            <textarea rows="4" placeholder="Enter your Message..." required></textarea>
                            <button type="submit">Submit</button>
                        </form>
                        
                    </div>
                </div>
            </div>
        </section>


        <section class="call-now">
            <div class="call-now-content">
                <div class="left animate-left">
                    <img src="images/callNow.svg" alt="Call Icon" class="call-icon" />
                    <h2>Call Now!</h2>
                </div>
                <div class="right animate-left">
                    <p class="mb-1">Revolutionize Your</p>
                    <a href="tel:+12062748655" class="phone">+1**************</a>
                </div>
            </div>
        </section>

        <footer class="footer">
            <div class="footer-container">
                <!-- Left -->
                <div class="footer-left">
                    <img src="images/OnCreativ_headerLogo.svg" alt="onCreativ Logo" class="footer-logo" />
                </div>

                <!-- Center -->
                <div class="footer-center">
                    <h3>Contact Us</h3>
                    <p><strong>Office Locations:</strong></p>
                    <p>5608 17th Avenue NW, Suite 717, Seattle, WA 98107</p>
                    <p>6543 S Las Vegas Blvd, Las Vegas, NV 89109</p>
                    <p><a href="tel:+12062748655">+1**************</a></p>
                    <!-- <p><a href="mailto:<EMAIL>"><EMAIL></a></p> -->
                </div>

                <!-- Right -->
                <div class="footer-right">
                    <h3>Contact time</h3>
                    <p>09:00 AM - 6:00 PM</p>
                    <p>Monday - Friday</p>
                    <p><strong>Follow Us</strong></p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i>




                    </div>





                    <script>
                        const animatedElements = document.querySelectorAll(
                            '.animate-left, .animate-right, .animate-top, .animate-bottom'
                        );

                        const observer = new IntersectionObserver((entries) => {
                            entries.forEach(entry => {
                                if (entry.isIntersecting) {
                                    entry.target.classList.add("animate");

                                    if (entry.target.id === 'bannerSection') {
                                        entry.target.classList.add("active");
                                    }
                                } else {
                                    entry.target.classList.remove("animate");
                                }
                            });
                        }, { threshold: 0.2 });

                        animatedElements.forEach(el => observer.observe(el));
                    </script>


                    <script>
                        (() => {
                            const navLinks = document.querySelectorAll('.nav-link');
                            const indicator = document.querySelector('.hover-indicator');

                            function moveIndicator(link) {
                                const rect = link.getBoundingClientRect();
                                const parentRect = link.parentElement.parentElement.getBoundingClientRect(); // ul
                                indicator.style.width = `${rect.width}px`;
                                indicator.style.left = `${rect.left - parentRect.left}px`;
                                indicator.style.opacity = 1;
                            }

                            navLinks.forEach(link => {
                                link.addEventListener('mouseenter', () => moveIndicator(link));
                            });

                            document.querySelector('.navbar-menu').addEventListener('mouseleave', () => {
                                indicator.style.opacity = 0;
                            });

                            // Initial move to active item
                            const activeLink = document.querySelector('.nav-link.active');
                            if (activeLink) {
                                moveIndicator(activeLink);
                            }
                        })();
                    </script>

                    <script>
                        const countingElements = document.querySelectorAll('.counting');

                        const countObserver = new IntersectionObserver((entries) => {
                            entries.forEach(entry => {
                                if (entry.isIntersecting) {
                                    const element = entry.target;
                                    const endValue = parseFloat(element.getAttribute('data-count'));
                                    const suffix = element.getAttribute('data-suffix') || "";
                                    const duration = 2000;
                                    const frameRate = 60;
                                    const totalFrames = Math.round(duration / (1000 / frameRate));
                                    let frame = 0;

                                    function animateCount() {
                                        frame++;
                                        let progress = frame / totalFrames;
                                        if (progress > 1) progress = 1;

                                        let currentValue = endValue * progress;

                                        element.textContent = endValue % 1 !== 0
                                            ? currentValue.toFixed(1) + suffix
                                            : Math.floor(currentValue) + suffix;

                                        if (progress < 1) {
                                            requestAnimationFrame(animateCount);
                                        }
                                    }

                                    // reset to 0 before starting
                                    element.textContent = "0" + suffix;
                                    animateCount();
                                }
                            });
                        }, { threshold: 0.3 });

                        countingElements.forEach(el => countObserver.observe(el));
                    </script>



                    <script>
                        function toggleMenu() {
                            const menu = document.getElementById('navbarMenu');
                            menu.classList.toggle('open');
                        }

                        document.addEventListener('DOMContentLoaded', () => {
                            const menu = document.getElementById('navbarMenu');
                            const toggleButton = document.querySelector('.navbar-toggle');

                            // Close menu when any nav link is clicked
                            document.querySelectorAll('#navbarMenu .nav-link').forEach(link => {
                                link.addEventListener('click', () => {
                                    menu.classList.remove('open');
                                });
                            });

                            // Close when clicking outside
                            document.addEventListener('click', (event) => {
                                if (
                                    menu.classList.contains('open') &&
                                    !menu.contains(event.target) &&
                                    !toggleButton.contains(event.target)
                                ) {
                                    menu.classList.remove('open');
                                }
                            });
                        });
                    </script>

                    <script>
                        // Show the button when scrolled down 20px from the top
                        window.onscroll = function () {
                            scrollFunction();
                        };

                        function scrollFunction() {
                            const scrollBtn = document.getElementById("scrollBtn");
                            if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                                scrollBtn.style.display = "block";
                            } else {
                                scrollBtn.style.display = "none";
                            }
                        }

                        // Scroll to the top of the document
                        function scrollToTop() {
                            document.body.scrollTop = 0; // For Safari
                            document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
                        }
                    </script>



<script>
    let slideIndex = 0;
    showSlides(slideIndex);

    function currentSlide(n) {
        showSlides(slideIndex = n);
    }

    function showSlides(n) {
        let slides = document.querySelectorAll(".slide");
        let dots = document.querySelectorAll(".dot");
        if (n >= slides.length) slideIndex = 0;
        if (n < 0) slideIndex = slides.length - 1;

        slides.forEach(slide => slide.classList.remove("active"));
        dots.forEach(dot => dot.classList.remove("active"));

        slides[slideIndex].classList.add("active");
        dots[slideIndex].classList.add("active");
    }

    // Auto-play every 4s
    setInterval(() => {
        slideIndex++;
        showSlides(slideIndex);
    }, 4000);
</script>









</body>

</html>